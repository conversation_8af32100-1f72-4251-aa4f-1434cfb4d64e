import mongoose, { Schema } from 'mongoose';

export const followerItemSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  usernameFollower: { 
    type: String, 
    required: true 
  },
  isFollower: { 
    type: Boolean, 
    default: true 
  }
}, { _id: false });

export const followingItemSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  usernameFollowing: { 
    type: String, 
    required: true 
  },
  isFollowing: { 
    type: Boolean, 
    default: true 
  }
}, { _id: false });
