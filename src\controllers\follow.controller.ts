import type { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import mongoose from 'mongoose';
import User from '../models/User';
import { AuthenticatedRequest } from '../middlewares/auth';

// Follow a user
export const followUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userIdToFollow } = req.body;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate userIdToFollow
    if (!userIdToFollow || !mongoose.Types.ObjectId.isValid(userIdToFollow)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid user ID to follow is required'
      });
      return;
    }

    // Check if trying to follow themselves
    if (currentUserId === userIdToFollow) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Cannot follow yourself'
      });
      return;
    }

    // Get both users
    const [currentUser, userToFollow] = await Promise.all([
      User.findById(currentUserId),
      User.findById(userIdToFollow)
    ]);

    if (!currentUser || !userToFollow) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Check if already following
    const isAlreadyFollowing = currentUser.following.some(
      (followingItem) => followingItem.userId.toString() === userIdToFollow
    );

    if (isAlreadyFollowing) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Already following this user'
      });
      return;
    }

    // Add to current user's following list
    currentUser.following.push({
      userId: userIdToFollow,
      usernameFollowing: userToFollow.username || userToFollow.email,
      isFollowing: true
    });
    currentUser.followingCount += 1;

    // Add to target user's followers list
    userToFollow.followers.push({
      userId: currentUserId,
      usernameFollower: currentUser.username || currentUser.email,
      isFollower: true
    });
    userToFollow.followersCount += 1;

    // Save both users
    await Promise.all([
      currentUser.save(),
      userToFollow.save()
    ]);

    res.status(StatusCodes.OK).json({
      message: 'Successfully followed user',
      followingCount: currentUser.followingCount,
      followedUser: {
        userId: userToFollow._id,
        username: userToFollow.username || userToFollow.email
      }
    });
  } catch (error) {
    console.error('Error following user:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error following user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Unfollow a user
export const unfollowUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userIdToUnfollow } = req.body;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate userIdToUnfollow
    if (!userIdToUnfollow || !mongoose.Types.ObjectId.isValid(userIdToUnfollow)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid user ID to unfollow is required'
      });
      return;
    }

    // Get both users
    const [currentUser, userToUnfollow] = await Promise.all([
      User.findById(currentUserId),
      User.findById(userIdToUnfollow)
    ]);

    if (!currentUser || !userToUnfollow) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Check if currently following
    const followingIndex = currentUser.following.findIndex(
      (followingItem) => followingItem.userId.toString() === userIdToUnfollow
    );

    if (followingIndex === -1) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Not following this user'
      });
      return;
    }

    // Remove from current user's following list
    currentUser.following.splice(followingIndex, 1);
    currentUser.followingCount = Math.max(0, currentUser.followingCount - 1);

    // Remove from target user's followers list
    const followerIndex = userToUnfollow.followers.findIndex(
      (followerItem) => followerItem.userId.toString() === currentUserId
    );

    if (followerIndex !== -1) {
      userToUnfollow.followers.splice(followerIndex, 1);
      userToUnfollow.followersCount = Math.max(0, userToUnfollow.followersCount - 1);
    }

    // Save both users
    await Promise.all([
      currentUser.save(),
      userToUnfollow.save()
    ]);

    res.status(StatusCodes.OK).json({
      message: 'Successfully unfollowed user',
      followingCount: currentUser.followingCount,
      unfollowedUser: {
        userId: userToUnfollow._id,
        username: userToUnfollow.username || userToUnfollow.email
      }
    });
  } catch (error) {
    console.error('Error unfollowing user:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error unfollowing user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get followers list
export const getFollowers = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const user = await User.findById(userId).select('followers followersCount');

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    const followersList = user.followers.map(follower => ({
      userId: follower.userId,
      usernameFollower: follower.usernameFollower
    }));

    res.status(StatusCodes.OK).json({
      message: 'Followers retrieved successfully',
      followers: followersList,
      followersCount: user.followersCount
    });
  } catch (error) {
    console.error('Error getting followers:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error getting followers',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get following list
export const getFollowing = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const user = await User.findById(userId).select('following followingCount');

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    const followingList = user.following.map(following => ({
      userId: following.userId,
      usernameFollowing: following.usernameFollowing
    }));

    res.status(StatusCodes.OK).json({
      message: 'Following list retrieved successfully',
      following: followingList,
      followingCount: user.followingCount
    });
  } catch (error) {
    console.error('Error getting following list:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error getting following list',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
