import mongoose, { Schema } from 'mongoose';
import { IUser } from '../interfaces/index';

const followerItemSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  usernameFollower: { 
    type: String, 
    required: true 
  },
  isFollower: { 
    type: Boolean, 
    default: true 
  }
}, { _id: false });

const followingItemSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  usernameFollowing: { 
    type: String, 
    required: true 
  },
  isFollowing: { 
    type: Boolean, 
    default: true 
  }
}, { _id: false });

const userSchema: Schema = new Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  otp: { type: String },
  otpCreatedAt: { type: Date },
  username: { type: String },
  gender: { type: String },
  dob: { type: Date },
  about: { type: String },
  habitCompletionPercentage: {
    type: Number,
    default: 0
  },
  // Follow data
  followers: [followerItemSchema],
  following: [followingItemSchema],
  followersCount: {
    type: Number,
    default: 0
  },
  followingCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const User = mongoose.model<IUser>('User', userSchema);
export default User;
