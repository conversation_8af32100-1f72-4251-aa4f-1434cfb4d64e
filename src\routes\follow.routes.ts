import { Router } from 'express';
import {
  followUser,
  unfollowUser,
  getFollowers,
  getFollowing
} from '../controllers/follow.controller';
import { authenticateToken } from '../middlewares/auth';

const router = Router();

// Apply JWT authentication middleware to all follow routes
router.use(authenticateToken);

// Follow operations
router.post('/follow', followUser);
router.post('/unfollow', unfollowUser);

// Get lists
router.get('/followers', getFollowers);
router.get('/following', getFollowing);

export default router;
