# Follow API Documentation

## Overview
The Follow API provides CRUD operations for managing user followers and following relationships with automatic count management and JWT authentication.

## Base URL
```
http://localhost:5001/api/follow
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Data Models

### User Model Updates
The User model now includes:
```typescript
{
  // ... existing fields
  followers: [
    {
      userId: ObjectId,
      usernameFollower: string,
      isFollower: boolean (default: true)
    }
  ],
  following: [
    {
      userId: ObjectId,
      usernameFollowing: string,
      isFollowing: boolean (default: true)
    }
  ],
  followersCount: number (default: 0),
  followingCount: number (default: 0)
}
```

## Endpoints

### 1. Follow User
**POST** `/api/follow/follow`

Follow another user. Automatically updates both users' followers/following arrays and counts.

**Request Body:**
```json
{
  "userIdToFollow": "60d5ecb74b24a1234567890a"
}
```

**Success Response (200):**
```json
{
  "message": "Successfully followed user",
  "followingCount": 5,
  "followedUser": {
    "userId": "60d5ecb74b24a1234567890a",
    "username": "john_doe"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid user ID, trying to follow yourself, or already following
- `401 Unauthorized`: Invalid or missing JWT token
- `404 Not Found`: User not found
- `500 Internal Server Error`: Server error

### 2. Unfollow User
**POST** `/api/follow/unfollow`

Unfollow a user. Automatically updates both users' followers/following arrays and counts.

**Request Body:**
```json
{
  "userIdToUnfollow": "60d5ecb74b24a1234567890a"
}
```

**Success Response (200):**
```json
{
  "message": "Successfully unfollowed user",
  "followingCount": 4,
  "unfollowedUser": {
    "userId": "60d5ecb74b24a1234567890a",
    "username": "john_doe"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid user ID or not following this user
- `401 Unauthorized`: Invalid or missing JWT token
- `404 Not Found`: User not found
- `500 Internal Server Error`: Server error

### 3. Get Followers List
**GET** `/api/follow/followers`

Get the list of users following the authenticated user.

**Success Response (200):**
```json
{
  "message": "Followers retrieved successfully",
  "followers": [
    {
      "userId": "60d5ecb74b24a1234567890a",
      "usernameFollower": "john_doe"
    },
    {
      "userId": "60d5ecb74b24a1234567890b",
      "usernameFollower": "jane_smith"
    }
  ],
  "followersCount": 2
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or missing JWT token
- `404 Not Found`: User not found
- `500 Internal Server Error`: Server error

### 4. Get Following List
**GET** `/api/follow/following`

Get the list of users that the authenticated user is following.

**Success Response (200):**
```json
{
  "message": "Following list retrieved successfully",
  "following": [
    {
      "userId": "60d5ecb74b24a1234567890c",
      "usernameFollowing": "alice_wonder"
    },
    {
      "userId": "60d5ecb74b24a1234567890d",
      "usernameFollowing": "bob_builder"
    }
  ],
  "followingCount": 2
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or missing JWT token
- `404 Not Found`: User not found
- `500 Internal Server Error`: Server error

## Implementation Details

### Automatic Count Management
- `followersCount` and `followingCount` are automatically updated when users follow/unfollow
- Counts are decremented safely using `Math.max(0, count - 1)` to prevent negative values

### Data Synchronization
- When User A follows User B:
  - User A's `following` array gets User B's info with `isFollowing: true`
  - User B's `followers` array gets User A's info with `isFollower: true`
  - Both users' counts are incremented

- When User A unfollows User B:
  - User A's `following` array removes User B's entry
  - User B's `followers` array removes User A's entry
  - Both users' counts are decremented

### Username Handling
- Uses `username` field if available, falls back to `email` if username is not set
- Stored as `usernameFollower` and `usernameFollowing` for clarity

### Security Features
- JWT authentication required for all endpoints
- Users cannot follow themselves
- Duplicate follow attempts are prevented
- Input validation for user IDs

## Example Usage

```javascript
// Follow a user
const followResponse = await fetch('/api/follow/follow', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_jwt_token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userIdToFollow: '60d5ecb74b24a1234567890a'
  })
});

// Get followers
const followersResponse = await fetch('/api/follow/followers', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your_jwt_token'
  }
});

// Get following list
const followingResponse = await fetch('/api/follow/following', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your_jwt_token'
  }
});

// Unfollow a user
const unfollowResponse = await fetch('/api/follow/unfollow', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_jwt_token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    userIdToUnfollow: '60d5ecb74b24a1234567890a'
  })
});
```
